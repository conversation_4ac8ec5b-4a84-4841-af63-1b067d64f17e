import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'

import { actionLogApi } from '@/entities/action-log'
import { Modal } from '@/shared/components'
import { <PERSON><PERSON>, Loader } from '@/shared/ui'
import { IActionLogRequestTypes } from '@/shared/types/store/settings'

import styles from './action-log-modal.module.scss'
import { addDays, endOfDay, startOfDay } from '@/shared/helpers/date'
import { SelectDateCardWithInput } from '@/shared/components/SelectDateCard/select-date-card-with-input'

const cx = classNamesBind.bind(styles)

type Props = {
  type: IActionLogRequestTypes
  modalOpen: boolean
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>
}

export const ActionLogModal = ({ type, modalOpen, setModalOpen }: Props) => {
  const { t } = useTranslation()

  const [step, setStep] = useState<number>(0)
  const [startDate, setStartDate] = useState<Date | null>()
  const [endDate, setEndDate] = useState<Date | null>()

  const [generateReport, { isLoading, isSuccess, reset, isError }] =
    actionLogApi.useGenerateReportByTypesMutation()

  const getGenerateReportId = async () => {
    await generateReport({
      report_type: type,
      date_from: startDate ? startDate.toISOString().slice(0, 10) : undefined,
      date_to: endDate ? endDate.toISOString().slice(0, 10) : undefined,
    }).unwrap()
  }

  const onDownload = () => {
    setStep(1)
    getGenerateReportId()
  }

  useEffect(() => {
    return () => {
      reset()
    }
  }, [])

  return (
    <Modal active={modalOpen} setActive={setModalOpen} className={cx('modal')}>
      <h2 className={cx('modal__title')}>{t('commons:export_report')}</h2>
      {step === 0 && (
        <div className={cx('modal__inner')}>
          <div className={cx('modal__intervals')}>
            <SelectDateCardWithInput
              label={t('commons:date_start')}
              wrapperClassName={cx('modal__interval')}
              datePickerClassName={cx('modal__datepicker')}
              classNameLabel={cx('modal__interval-label')}
              onChange={v => {
                const date_start = v ?? null
                setStartDate(date_start)
              }}
              withoutTime
              selected={startDate ?? null}
              text={t('commons:choose')}
              max={
                endDate ? endOfDay(addDays(new Date(endDate as Date), -1)) : startOfDay(new Date())
              }
              min={null}
            />
            <div className={cx('modal__separator')}>:</div>
            <SelectDateCardWithInput
              label={t('commons:date_end')}
              wrapperClassName={cx('modal__interval')}
              datePickerClassName={cx('modal__datepicker')}
              classNameLabel={cx('modal__interval-label')}
              onChange={v => {
                const date_end = v ?? null
                setEndDate(date_end)
              }}
              withoutTime
              selected={endDate ?? null}
              text={t('commons:choose')}
              max={endOfDay(new Date())}
              min={startDate ? startOfDay(addDays(new Date(startDate), 1)) : null}
            />
          </div>
          <div className={cx('modal__buttons')}>
            <Button size='big' color='gray' onClick={() => setModalOpen(false)}>
              {t('commons:cancel')}
            </Button>
            <Button size='big' color='green' onClick={onDownload}>
              {t('commons:download')}
            </Button>
          </div>
        </div>
      )}
      {step === 1 && (
        <div className={cx('modal__content')}>
          {isLoading && (
            <>
              <Loader size='56' />
              <h3 className={cx('modal__loading')}>{t('commons:report_generation')}</h3>
            </>
          )}
          {isSuccess && (
            <>
              <Loader size='56' success loading={false} />
              <h3 className={cx('modal__success')}>
                {' '}
                {t('commons:report_in_progress')}
                <br />
                {t('commons:report_description')}
              </h3>
            </>
          )}
          {isError && (
            <>
              <Loader size='56' error loading={false} />
              <h3 className={cx('modal__error')}>{t('commons:error_unexpected')}</h3>
            </>
          )}
        </div>
      )}
    </Modal>
  )
}
